

<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en" > <!--<![endif]-->
<head>
  <meta charset="utf-8">
  
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>mujoco-py Documentation &mdash; mujoco-py ******** documentation</title>
  

  
  
  
  

  

  
  
    

  

  
  
    <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  

  

  
        <link rel="index" title="Index"
              href="genindex.html"/>
        <link rel="search" title="Search" href="search.html"/>
    <link rel="top" title="mujoco-py ******** documentation" href="#"/>
        <link rel="next" title="API reference" href="reference.html"/> 

  
  <script src="_static/js/modernizr.min.js"></script>

</head>

<body class="wy-body-for-nav" role="document">

   
  <div class="wy-grid-for-nav">

    
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search">
          

          
            <a href="#" class="icon icon-home"> mujoco-py
          

          
          </a>

          
            
            
              <div class="version">
                ********
              </div>
            
          

          
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        <div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
          
            
            
              
            
            
              <ul>
<li class="toctree-l1"><a class="reference internal" href="reference.html">API reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="internals.html">Internals</a></li>
</ul>

            
          
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap">

      
      <nav class="wy-nav-top" role="navigation" aria-label="top navigation">
        
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="#">mujoco-py</a>
        
      </nav>


      
      <div class="wy-nav-content">
        <div class="rst-content">
          















<div role="navigation" aria-label="breadcrumbs navigation">

  <ul class="wy-breadcrumbs">
    
      <li><a href="#">Docs</a> &raquo;</li>
        
      <li>mujoco-py Documentation</li>
    
    
      <li class="wy-breadcrumbs-aside">
        
            
            <a href="_sources/index.rst.txt" rel="nofollow"> View page source</a>
          
        
      </li>
    
  </ul>

  
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
            
  <div class="section" id="mujoco-py-documentation">
<h1>mujoco-py Documentation<a class="headerlink" href="#mujoco-py-documentation" title="Permalink to this headline">¶</a></h1>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="reference.html">API reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="reference.html#mjsim-basic-simulation">MjSim: Basic simulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="reference.html#pymjdata-time-dependent-data">PyMjData: Time-dependent data</a></li>
<li class="toctree-l2"><a class="reference internal" href="reference.html#mjsimpool-batched-simulation">MjSimPool: Batched simulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="reference.html#mjviewer-3d-rendering">MjViewer: 3D rendering</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="internals.html">Internals</a><ul>
<li class="toctree-l2"><a class="reference internal" href="internals.html#autogenerated-wrappers">Autogenerated wrappers</a></li>
</ul>
</li>
</ul>
</div>
<p><a class="reference external" href="http://mujoco.org/">MuJoCo</a> is a physics engine for detailed, efficient rigid body simulations with contacts. <code class="docutils literal"><span class="pre">mujoco-py</span></code> allows using MuJoCo from Python 3.</p>
<p>See the <a class="reference external" href="https://github.com/openai/mujoco-py/blob/master/README.md">README</a> for installation instructions and example usage.</p>
<p><code class="docutils literal"><span class="pre">mujoco-py</span></code> allows access to MuJoCo on a number of different levels of abstraction:</p>
<ul class="simple">
<li>Directly from Cython (low-level): <a class="reference external" href="https://github.com/openai/mujoco-py/tree/master/mujoco_py/pxd">Raw Cython declarations</a> are provided for using the MuJoCo C structs and functions directly in your own Cython code.</li>
<li>Using <a class="reference internal" href="reference.html#pymjdata"><span class="std std-ref">PyMjData: Time-dependent data</span></a> (medium-level): These wrappers are lightweight Cython <code class="docutils literal"><span class="pre">cdef</span></code> classes that expose MuJuCo data to Python space. The data in the MuJoCo structs is exposed as NumPy arrays bound to Mujoco-allocated memory, so there is no copying overhead when accessing or modifying MuJoCo state from Python. For more information on how this works internally, see [this document](./doc/cython_wrappers.md).</li>
<li>Using <a class="reference internal" href="reference.html#mujoco_py.MjSim" title="mujoco_py.MjSim"><code class="xref py py-class docutils literal"><span class="pre">mujoco_py.MjSim</span></code></a> (high-level): <a class="reference internal" href="reference.html#mujoco_py.MjSim" title="mujoco_py.MjSim"><code class="xref py py-class docutils literal"><span class="pre">mujoco_py.MjSim</span></code></a> manages a stateful simulation similar to the <a class="reference external" href="https://github.com/openai/gym/blob/master/gym/envs/mujoco/mujoco_env.py">MujocoEnv</a> class found in <a class="reference external" href="https://github.com/openai/gym">Gym</a></li>
</ul>
<p>.</p>
</div>


           </div>
           <div class="articleComments">
            
           </div>
          </div>
          <footer>
  
    <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
      
        <a href="reference.html" class="btn btn-neutral float-right" title="API reference" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right"></span></a>
      
      
    </div>
  

  <hr/>

  <div role="contentinfo">
    <p>
        &copy; Copyright 2017, OpenAI.

    </p>
  </div>
  Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a href="https://github.com/snide/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the Docs</a>. 

</footer>

        </div>
      </div>

    </section>

  </div>
  


  

    <script type="text/javascript">
        var DOCUMENTATION_OPTIONS = {
            URL_ROOT:'./',
            VERSION:'********',
            COLLAPSE_INDEX:false,
            FILE_SUFFIX:'.html',
            HAS_SOURCE:  true,
            SOURCELINK_SUFFIX: '.txt'
        };
    </script>
      <script type="text/javascript" src="_static/jquery.js"></script>
      <script type="text/javascript" src="_static/underscore.js"></script>
      <script type="text/javascript" src="_static/doctools.js"></script>

  

  
  
    <script type="text/javascript" src="_static/js/theme.js"></script>
  

  
  
  <script type="text/javascript">
      jQuery(function () {
          SphinxRtdTheme.StickyNav.enable();
      });
  </script>
   

</body>
</html>