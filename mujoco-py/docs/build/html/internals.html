

<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en" > <!--<![endif]-->
<head>
  <meta charset="utf-8">
  
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>Internals &mdash; mujoco-py ******** documentation</title>
  

  
  
  
  

  

  
  
    

  

  
  
    <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  

  

  
        <link rel="index" title="Index"
              href="genindex.html"/>
        <link rel="search" title="Search" href="search.html"/>
    <link rel="top" title="mujoco-py ******** documentation" href="index.html"/>
        <link rel="prev" title="API reference" href="reference.html"/> 

  
  <script src="_static/js/modernizr.min.js"></script>

</head>

<body class="wy-body-for-nav" role="document">

   
  <div class="wy-grid-for-nav">

    
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search">
          

          
            <a href="index.html" class="icon icon-home"> mujoco-py
          

          
          </a>

          
            
            
              <div class="version">
                ********
              </div>
            
          

          
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        <div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
          
            
            
              
            
            
              <ul class="current">
<li class="toctree-l1"><a class="reference internal" href="reference.html">API reference</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Internals</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#autogenerated-wrappers">Autogenerated wrappers</a></li>
</ul>
</li>
</ul>

            
          
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap">

      
      <nav class="wy-nav-top" role="navigation" aria-label="top navigation">
        
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">mujoco-py</a>
        
      </nav>


      
      <div class="wy-nav-content">
        <div class="rst-content">
          















<div role="navigation" aria-label="breadcrumbs navigation">

  <ul class="wy-breadcrumbs">
    
      <li><a href="index.html">Docs</a> &raquo;</li>
        
      <li>Internals</li>
    
    
      <li class="wy-breadcrumbs-aside">
        
            
            <a href="_sources/internals.rst.txt" rel="nofollow"> View page source</a>
          
        
      </li>
    
  </ul>

  
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
            
  <div class="section" id="internals">
<h1>Internals<a class="headerlink" href="#internals" title="Permalink to this headline">¶</a></h1>
<div class="section" id="autogenerated-wrappers">
<span id="genwrapper"></span><h2>Autogenerated wrappers<a class="headerlink" href="#autogenerated-wrappers" title="Permalink to this headline">¶</a></h2>
<p>The Cython struct wrappers are generated by <code class="docutils literal"><span class="pre">scripts/gen_wrappers.py</span></code>. To illustrate how the wrappers work, let’s consider a toy C structure and the corresponding generated Cython cdef class. Here’s a stripped down version of <code class="docutils literal"><span class="pre">mjData</span></code> with a scalar member and a pointer (array) member:</p>
<div class="highlight-default"><div class="highlight"><pre><span></span><span class="n">typedef</span> <span class="n">struct</span> <span class="n">_mjData</span> <span class="p">{</span>
    <span class="nb">int</span> <span class="n">ne</span><span class="p">;</span>
    <span class="n">mjtNum</span><span class="o">*</span> <span class="n">qpos</span><span class="p">;</span>  <span class="o">//</span> <span class="p">(</span><span class="n">nq</span> <span class="n">x</span> <span class="mi">1</span><span class="p">)</span>
<span class="p">}</span> <span class="n">mjData</span><span class="p">;</span>
</pre></div>
</div>
<p>Here’s the corresponding generated Cython wrapper code:</p>
<div class="highlight-default"><div class="highlight"><pre><span></span><span class="n">cdef</span> <span class="k">class</span> <span class="nc">PyMjData</span><span class="p">(</span><span class="nb">object</span><span class="p">):</span>
    <span class="n">cdef</span> <span class="n">mjData</span><span class="o">*</span> <span class="n">ptr</span>
    <span class="n">cdef</span> <span class="n">mjModel</span><span class="o">*</span> <span class="n">_model</span>
    <span class="n">cdef</span> <span class="n">np</span><span class="o">.</span><span class="n">ndarray</span> <span class="n">_qpos</span>

    <span class="n">cdef</span> <span class="n">void</span> <span class="n">_set</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">mjData</span><span class="o">*</span> <span class="n">p</span><span class="p">,</span> <span class="n">mjModel</span><span class="o">*</span> <span class="n">model</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">ptr</span> <span class="o">=</span> <span class="n">p</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_model</span> <span class="o">=</span> <span class="n">model</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">_qpos</span> <span class="o">=</span> <span class="n">_wrap_mjtNum_1d</span><span class="p">(</span><span class="n">p</span><span class="o">.</span><span class="n">qpos</span><span class="p">,</span> <span class="n">model</span><span class="o">.</span><span class="n">nq</span><span class="p">)</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">ne</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span> <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">ptr</span><span class="o">.</span><span class="n">ne</span>

    <span class="nd">@ne</span><span class="o">.</span><span class="n">setter</span>
    <span class="k">def</span> <span class="nf">ne</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="nb">int</span> <span class="n">x</span><span class="p">):</span> <span class="bp">self</span><span class="o">.</span><span class="n">ptr</span><span class="o">.</span><span class="n">ne</span> <span class="o">=</span> <span class="n">x</span>

    <span class="nd">@property</span>
    <span class="k">def</span> <span class="nf">qpos</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span> <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">_qpos</span>

<span class="n">cdef</span> <span class="n">PyMjData</span> <span class="n">WrapMjData</span><span class="p">(</span><span class="n">mjData</span><span class="o">*</span> <span class="n">p</span><span class="p">,</span> <span class="n">mjModel</span><span class="o">*</span> <span class="n">model</span><span class="p">):</span>
    <span class="n">cdef</span> <span class="n">PyMjData</span> <span class="n">o</span> <span class="o">=</span> <span class="n">PyMjData</span><span class="p">()</span>
    <span class="n">o</span><span class="o">.</span><span class="n">_set</span><span class="p">(</span><span class="n">p</span><span class="p">,</span> <span class="n">model</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">o</span>
</pre></div>
</div>
<p><code class="docutils literal"><span class="pre">PyMjData</span></code> is the wrapper class for exposing the underlying Mujoco structure to Python; it doesn’t perform any memory mangement. A user writing Cython code can create this wrapper using <code class="docutils literal"><span class="pre">WrapMjData</span></code>. A <code class="docutils literal"><span class="pre">mjModel</span></code> pointer must be passed because the shape of a <code class="docutils literal"><span class="pre">mjData</span></code> member, namely <code class="docutils literal"><span class="pre">qpos</span></code>, depends on <code class="docutils literal"><span class="pre">model-&gt;nq</span></code>.</p>
<p>Each field of <code class="docutils literal"><span class="pre">mjData</span></code> corresponds to some generated piece of code in <code class="docutils literal"><span class="pre">PyMjData</span></code> that depends on the type of that field. For example, <code class="docutils literal"><span class="pre">ne</span></code> is a scalar integer, so it gets exposed as a pair of getter and setter methods in <code class="docutils literal"><span class="pre">PyMjData</span></code>. <code class="docutils literal"><span class="pre">qpos</span></code> is an array represented as a pointer to its first element, so it’s wrapped with a NumPy array by <code class="docutils literal"><span class="pre">_wrap_mjtNum_1d</span></code> and is exposed with a getter for that NumPy array.</p>
<p>The function <code class="docutils literal"><span class="pre">_wrap_mjtNum_1d</span></code> creates a Cython memoryview from the data pointer and converts it to a NumPy array pointing to the same memory:</p>
<div class="highlight-default"><div class="highlight"><pre><span></span><span class="n">cdef</span> <span class="n">inline</span> <span class="n">np</span><span class="o">.</span><span class="n">ndarray</span> <span class="n">_wrap_mjtNum_1d</span><span class="p">(</span><span class="n">mjtNum</span><span class="o">*</span> <span class="n">a</span><span class="p">,</span> <span class="nb">int</span> <span class="n">shape0</span><span class="p">):</span>
    <span class="k">if</span> <span class="n">shape0</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span> <span class="k">return</span> <span class="kc">None</span>
    <span class="n">cdef</span> <span class="n">mjtNum</span><span class="p">[:]</span> <span class="n">b</span> <span class="o">=</span> <span class="o">&lt;</span><span class="n">mjtNum</span><span class="p">[:</span><span class="n">shape0</span><span class="p">]</span><span class="o">&gt;</span> <span class="n">a</span>
    <span class="k">return</span> <span class="n">np</span><span class="o">.</span><span class="n">asarray</span><span class="p">(</span><span class="n">b</span><span class="p">)</span>
</pre></div>
</div>
<p>Similar functions for other types are also generated as required.</p>
<p>Keep in mind that the only reason to use these autogenerated wrappers is to allow Python users of the Cython code to easily access Mujoco data (for instance the <code class="docutils literal"><span class="pre">MjSim</span></code> Cython class, found in <a href="#id1"><span class="problematic" id="id2">``</span></a>cymj/cymj.pyx`). If you’re writing Cython code and you don’t need the user to access Mujoco data from Python, then there is no reason to use these wrappers.</p>
</div>
</div>


           </div>
           <div class="articleComments">
            
           </div>
          </div>
          <footer>
  
    <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
      
      
        <a href="reference.html" class="btn btn-neutral" title="API reference" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left"></span> Previous</a>
      
    </div>
  

  <hr/>

  <div role="contentinfo">
    <p>
        &copy; Copyright 2017, OpenAI.

    </p>
  </div>
  Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a href="https://github.com/snide/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the Docs</a>. 

</footer>

        </div>
      </div>

    </section>

  </div>
  


  

    <script type="text/javascript">
        var DOCUMENTATION_OPTIONS = {
            URL_ROOT:'./',
            VERSION:'********',
            COLLAPSE_INDEX:false,
            FILE_SUFFIX:'.html',
            HAS_SOURCE:  true,
            SOURCELINK_SUFFIX: '.txt'
        };
    </script>
      <script type="text/javascript" src="_static/jquery.js"></script>
      <script type="text/javascript" src="_static/underscore.js"></script>
      <script type="text/javascript" src="_static/doctools.js"></script>

  

  
  
    <script type="text/javascript" src="_static/js/theme.js"></script>
  

  
  
  <script type="text/javascript">
      jQuery(function () {
          SphinxRtdTheme.StickyNav.enable();
      });
  </script>
   

</body>
</html>