Search.setIndex({docnames:["index","internals","reference"],envversion:53,filenames:["index.rst","internals.rst","reference.rst"],objects:{"":{act:[2,0,1,""],act_dot:[2,0,1,""],active_contacts_efc_pos:[2,0,1,""],actuator_force:[2,0,1,""],actuator_length:[2,0,1,""],actuator_moment:[2,0,1,""],actuator_velocity:[2,0,1,""],body_jacp:[2,0,1,""],body_jacr:[2,0,1,""],body_xmat:[2,0,1,""],body_xpos:[2,0,1,""],body_xquat:[2,0,1,""],body_xvelp:[2,0,1,""],body_xvelr:[2,0,1,""],cacc:[2,0,1,""],cam_xmat:[2,0,1,""],cam_xpos:[2,0,1,""],cdof:[2,0,1,""],cdof_dot:[2,0,1,""],cfrc_ext:[2,0,1,""],cfrc_int:[2,0,1,""],cinert:[2,0,1,""],contact:[2,0,1,""],crb:[2,0,1,""],ctrl:[2,0,1,""],cvel:[2,0,1,""],efc_AR:[2,0,1,""],efc_AR_colind:[2,0,1,""],efc_AR_rowadr:[2,0,1,""],efc_AR_rownnz:[2,0,1,""],efc_D:[2,0,1,""],efc_J:[2,0,1,""],efc_JT:[2,0,1,""],efc_JT_colind:[2,0,1,""],efc_JT_rowadr:[2,0,1,""],efc_JT_rownnz:[2,0,1,""],efc_J_colind:[2,0,1,""],efc_J_rowadr:[2,0,1,""],efc_J_rownnz:[2,0,1,""],efc_R:[2,0,1,""],efc_aref:[2,0,1,""],efc_b:[2,0,1,""],efc_diagApprox:[2,0,1,""],efc_force:[2,0,1,""],efc_frictionloss:[2,0,1,""],efc_id:[2,0,1,""],efc_margin:[2,0,1,""],efc_solimp:[2,0,1,""],efc_solref:[2,0,1,""],efc_state:[2,0,1,""],efc_type:[2,0,1,""],efc_vel:[2,0,1,""],energy:[2,0,1,""],geom_jacp:[2,0,1,""],geom_jacr:[2,0,1,""],geom_xmat:[2,0,1,""],geom_xpos:[2,0,1,""],geom_xvelp:[2,0,1,""],geom_xvelr:[2,0,1,""],get_body_jacp:[2,1,1,""],get_body_jacr:[2,1,1,""],get_body_ximat:[2,1,1,""],get_body_xipos:[2,1,1,""],get_body_xmat:[2,1,1,""],get_body_xpos:[2,1,1,""],get_body_xquat:[2,1,1,""],get_body_xvelp:[2,1,1,""],get_body_xvelr:[2,1,1,""],get_cam_xmat:[2,1,1,""],get_cam_xpos:[2,1,1,""],get_camera_xmat:[2,1,1,""],get_camera_xpos:[2,1,1,""],get_geom_jacp:[2,1,1,""],get_geom_jacr:[2,1,1,""],get_geom_xmat:[2,1,1,""],get_geom_xpos:[2,1,1,""],get_geom_xvelp:[2,1,1,""],get_geom_xvelr:[2,1,1,""],get_joint_qpos:[2,1,1,""],get_joint_qvel:[2,1,1,""],get_joint_xanchor:[2,1,1,""],get_joint_xaxis:[2,1,1,""],get_light_xdir:[2,1,1,""],get_light_xpos:[2,1,1,""],get_mocap_pos:[2,1,1,""],get_mocap_quat:[2,1,1,""],get_site_jacp:[2,1,1,""],get_site_jacr:[2,1,1,""],get_site_xmat:[2,1,1,""],get_site_xpos:[2,1,1,""],get_site_xvelp:[2,1,1,""],get_site_xvelr:[2,1,1,""],light_xdir:[2,0,1,""],light_xpos:[2,0,1,""],maxuse_con:[2,0,1,""],maxuse_efc:[2,0,1,""],maxuse_stack:[2,0,1,""],mocap_pos:[2,0,1,""],mocap_quat:[2,0,1,""],nbuffer:[2,0,1,""],ncon:[2,0,1,""],ne:[2,0,1,""],nefc:[2,0,1,""],nf:[2,0,1,""],nstack:[2,0,1,""],pstack:[2,0,1,""],qLD:[2,0,1,""],qLDiagInv:[2,0,1,""],qLDiagSqrtInv:[2,0,1,""],qM:[2,0,1,""],qacc:[2,0,1,""],qacc_unc:[2,0,1,""],qacc_warmstart:[2,0,1,""],qfrc_actuator:[2,0,1,""],qfrc_applied:[2,0,1,""],qfrc_bias:[2,0,1,""],qfrc_constraint:[2,0,1,""],qfrc_inverse:[2,0,1,""],qfrc_passive:[2,0,1,""],qfrc_unc:[2,0,1,""],qpos:[2,0,1,""],qvel:[2,0,1,""],sensordata:[2,0,1,""],set_joint_qpos:[2,0,1,""],set_joint_qvel:[2,0,1,""],set_mocap_pos:[2,0,1,""],set_mocap_quat:[2,0,1,""],site_jacp:[2,0,1,""],site_jacr:[2,0,1,""],site_xmat:[2,0,1,""],site_xpos:[2,0,1,""],site_xvelp:[2,0,1,""],site_xvelr:[2,0,1,""],solver:[2,0,1,""],solver_fwdinv:[2,0,1,""],solver_iter:[2,0,1,""],solver_nnz:[2,0,1,""],subtree_angmom:[2,0,1,""],subtree_com:[2,0,1,""],subtree_linvel:[2,0,1,""],ten_length:[2,0,1,""],ten_moment:[2,0,1,""],ten_velocity:[2,0,1,""],ten_wrapadr:[2,0,1,""],ten_wrapnum:[2,0,1,""],time:[2,0,1,""],timer:[2,0,1,""],userdata:[2,0,1,""],warning:[2,0,1,""],wrap_obj:[2,0,1,""],wrap_xpos:[2,0,1,""],xanchor:[2,0,1,""],xaxis:[2,0,1,""],xfrc_applied:[2,0,1,""],ximat:[2,0,1,""],xipos:[2,0,1,""]},"mujoco_py.MjSim":{get_state:[2,1,1,""],render:[2,1,1,""],reset:[2,1,1,""],save:[2,1,1,""],set_state:[2,1,1,""],set_state_from_flattened:[2,1,1,""],step:[2,1,1,""]},"mujoco_py.MjSimPool":{create_from_sim:[2,3,1,""],forward:[2,1,1,""],reset:[2,1,1,""],step:[2,1,1,""]},"mujoco_py.MjViewer":{render:[2,1,1,""]},"mujoco_py.MjViewerBasic":{render:[2,1,1,""]},mujoco_py:{MjSim:[2,2,1,""],MjSimPool:[2,2,1,""],MjSimState:[2,2,1,""],MjViewer:[2,2,1,""],MjViewerBasic:[2,2,1,""],ignore_mujoco_warnings:[2,4,1,""],load_model_from_mjb:[2,4,1,""],load_model_from_path:[2,4,1,""],load_model_from_xml:[2,4,1,""]}},objnames:{"0":["py","attribute","Python attribute"],"1":["py","method","Python method"],"2":["py","class","Python class"],"3":["py","staticmethod","Python static method"],"4":["py","function","Python function"]},objtypes:{"0":"py:attribute","1":"py:method","2":"py:class","3":"py:staticmethod","4":"py:function"},terms:{"abstract":0,"byte":2,"class":[0,1,2],"default":2,"float":2,"function":[0,1,2],"int":[1,2],"return":[1,2],"static":2,"switch":2,"true":2,"void":1,For:[0,1,2],The:[0,1,2],Their:2,These:0,Useful:2,Using:0,Will:2,_mjdata:1,_model:1,_qpo:1,_set:1,_wrap_mjtnum_1d:1,access:[0,1,2],act:2,act_dot:2,active_contacts_efc_po:2,actuator_forc:2,actuator_length:2,actuator_mo:2,actuator_veloc:2,add:2,advanc:2,after:2,all:2,alloc:0,allow:[0,1],also:[1,2],ani:1,api:0,appli:2,arg:2,arrai:[0,1,2],asarrai:1,asset:2,attribut:2,autogener:[0,2],automat:2,basic:0,batch:0,becaus:1,been:2,befor:2,behind:2,between:2,bind:2,bodi:[0,2],body_jacp:2,body_jacr:2,body_xmat:2,body_xpo:2,body_xquat:2,body_xvelp:2,body_xvelr:2,bool:2,bound:0,buffer:2,cacc:2,call:2,call_forward:2,callback:2,cam:2,cam_xmat:2,cam_xpo:2,camera:2,camera_nam:2,can:1,captur:2,cdef:[0,1],cdof:2,cdof_dot:2,cfrc_ext:2,cfrc_int:2,cinert:2,clear:2,clone:2,code:[0,1],compon:2,consid:[1,2],contact:[0,2],contain:2,control:2,convert:1,copi:[0,2],correspond:[1,2],crb:2,creat:[1,2],create_from_sim:2,ctrl:2,current:2,custom:2,cvel:2,cymj:1,cython:[0,1],cython_wrapp:0,data:[0,1],debug:2,debugg:2,declar:0,decreas:2,def:1,defens:2,defin:2,depend:[0,1],depth:2,deriv:2,desir:2,detail:0,dict:2,differ:0,directli:[0,2],disabl:2,displai:2,doc:0,doesn:[1,2],don:1,down:1,drop:2,dure:2,dynam:2,each:1,easili:1,efc_ar:2,efc_ar_colind:2,efc_ar_rowadr:2,efc_ar_rownnz:2,efc_aref:2,efc_b:2,efc_d:2,efc_diagapprox:2,efc_forc:2,efc_frictionloss:2,efc_id:2,efc_j:2,efc_j_colind:2,efc_j_rowadr:2,efc_j_rownnz:2,efc_jt:2,efc_jt_colind:2,efc_jt_rowadr:2,efc_jt_rownnz:2,efc_margin:2,efc_r:2,efc_solimp:2,efc_solref:2,efc_stat:2,efc_typ:2,efc_vel:2,effici:0,either:2,element:1,enabl:2,encod:2,energi:2,engin:0,entri:2,everi:2,exampl:[0,1],except:2,expos:[0,1],extend:2,fals:2,field:1,file:2,first:[1,2],follow:2,forc:2,format:2,forward:2,found:[0,1],frame:2,free:2,from:[0,1,2],gen_wrapp:1,gener:[1,2],geom:2,geom_jacp:2,geom_jacr:2,geom_xmat:2,geom_xpo:2,geom_xvelp:2,geom_xvelr:2,get:[1,2],get_body_jacp:2,get_body_jacr:2,get_body_ximat:2,get_body_xipo:2,get_body_xmat:2,get_body_xpo:2,get_body_xquat:2,get_body_xvelp:2,get_body_xvelr:2,get_cam_xmat:2,get_cam_xpo:2,get_camera_xmat:2,get_camera_xpo:2,get_geom_jacp:2,get_geom_jacr:2,get_geom_xmat:2,get_geom_xpo:2,get_geom_xvelp:2,get_geom_xvelr:2,get_joint_qpo:2,get_joint_qvel:2,get_joint_xanchor:2,get_joint_xaxi:2,get_light_xdir:2,get_light_xpo:2,get_mjb:2,get_mocap_po:2,get_mocap_quat:2,get_site_jacp:2,get_site_jacr:2,get_site_xmat:2,get_site_xpo:2,get_site_xvelp:2,get_site_xvelr:2,get_stat:2,get_xml:2,getter:1,given:2,gui:2,gym:[0,2],have:2,header:2,height:2,helper:2,here:1,hide:2,high:0,histor:2,how:[0,1],ignor:2,ignore_mujoco_warn:2,illustr:1,imag:2,includ:2,increas:2,individu:2,inerti:2,inform:[0,2],inlin:1,inner:2,instal:0,instanc:1,instruct:0,integ:1,interact:2,intern:[0,2],ipdb:2,its:[1,2],jacp:2,jacr:2,joint:2,keep:[1,2],keep_inerti:2,kei:2,keyfram:2,lag:2,larg:2,let:1,level:0,light:2,light_xdir:2,light_xpo:2,lightweight:0,like:2,list:2,load:2,load_model_from_mjb:2,load_model_from_path:2,load_model_from_xml:2,loop:2,low:0,main:2,make:2,manag:0,mangement:1,markup:2,maxuse_con:2,maxuse_efc:2,maxuse_stack:2,medium:0,member:1,memori:[0,1],memoryview:1,mesh:2,method:[1,2],mind:1,mj_forward:2,mj_step:2,mjb:2,mjdata:1,mjmodel:[1,2],mjsim:[0,1],mjsimpool:0,mjsimstat:2,mjtnum:1,mjviewer:0,mjviewerbas:2,mocap:2,mocap_po:2,mocap_quat:2,model:[1,2],modifi:[0,2],more:[0,2],mous:2,movabl:2,mujoco:[1,2],mujoco_pi:[0,2],mujocoenv:[0,2],mujuco:0,multipl:2,must:1,name:[1,2],nativ:2,nbuffer:2,ncon:2,ndarrai:[1,2],need:1,nefc:2,next:2,none:[1,2],note:2,nsim:2,nstack:2,nsubstep:2,number:[0,2],numpi:[0,1,2],object:[1,2],off:2,onc:2,one:2,ones:2,onli:[1,2],oper:2,option:2,other:1,over:2,overhead:[0,2],own:0,pair:1,parallel:2,paramet:2,part:2,pass:1,path:2,paus:2,per:2,perform:1,physic:0,pickl:2,piec:1,playback:2,point:1,pointer:1,pool:2,pos:2,previous:2,process:2,properti:[1,2],prototyp:2,provid:[0,2],pstack:2,ptr:1,pymjdata:[0,1],pymjmodel:2,python:[0,1],pyx:1,qacc:2,qacc_unc:2,qacc_warmstart:2,qfrc_actuat:2,qfrc_appli:2,qfrc_bia:2,qfrc_constraint:2,qfrc_invers:2,qfrc_passiv:2,qfrc_unc:2,qld:2,qldiaginv:2,qldiagsqrtinv:2,qpo:[1,2],quat:2,quickli:2,qvel:2,raw:0,readm:0,real:2,reason:1,receiv:2,record:2,refer:0,relat:2,remov:2,render:0,repres:[1,2],requir:[1,2],reset:2,reward:2,rgb:2,right:2,rigid:0,rollout:2,run:2,same:1,save:2,scalar:1,scene:2,scope:2,screen:2,screenshot:2,script:1,see:[0,2],self:1,sensordata:2,set:2,set_joint_qpo:2,set_joint_qvel:2,set_mocap_po:2,set_mocap_quat:2,set_stat:2,set_state_from_flatten:2,setter:1,shape0:1,shape:1,should:2,show:2,sim:2,similar:[0,1,2],simpl:2,simul:0,sinc:2,site:2,site_jacp:2,site_jacr:2,site_xmat:2,site_xpo:2,site_xvelp:2,site_xvelr:2,skip:2,snapshot:2,solver:2,solver_fwdinv:2,solver_it:2,solver_nnz:2,some:1,sophist:2,sourc:2,space:[0,2],specifi:2,speed:2,start:2,state:[0,2],step:2,stop:2,str:2,stream:2,string:2,strip:1,struct:[0,1,2],structur:[1,2],substanti:2,substep:2,subtree_angmom:2,subtree_com:2,subtree_linvel:2,support:2,swap:2,tab:2,ten_length:2,ten_moment:2,ten_veloc:2,ten_wrapadr:2,ten_wrapnum:2,textur:2,than:2,them:2,therefor:2,thi:[0,1,2],time:0,timer:2,togeth:2,toggl:2,toi:1,total:2,transpar:2,turn:2,type:1,typedef:1,udd:2,udd_callback:2,udd_stat:2,uint8:2,underli:1,unflatten:2,unpaus:2,usag:0,use:[1,2],used:2,useful:2,user:[1,2],userdata:2,using:[0,1,2],util:2,valu:2,vector:2,version:1,video:2,view:2,visual:2,want:2,warn:2,well:2,were:2,when:[0,2],which:2,width:2,within:2,without:2,work:[0,1],wrap:[1,2],wrap_obj:2,wrap_xpo:2,wrapmjdata:1,wrapper:[0,2],write:[1,2],xanchor:2,xaxi:2,xdir:2,xfrc_appli:2,ximat:2,xipo:2,xmat:2,xml:2,xml_string:2,xpo:2,xquat:2,xvelp:2,xvelr:2,you:[1,2],your:[0,2]},titles:["mujoco-py Documentation","Internals","API reference"],titleterms:{api:2,autogener:1,basic:2,batch:2,data:2,depend:2,document:0,intern:1,mjsim:2,mjsimpool:2,mjviewer:2,mujoco:0,pymjdata:2,refer:2,render:2,simul:2,time:2,wrapper:1}})