


<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en" > <!--<![endif]-->
<head>
  <meta charset="utf-8">
  
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>Index &mdash; mujoco-py ******** documentation</title>
  

  
  
  
  

  

  
  
    

  

  
  
    <link rel="stylesheet" href="_static/css/theme.css" type="text/css" />
  

  

  
        <link rel="index" title="Index"
              href="#"/>
        <link rel="search" title="Search" href="search.html"/>
    <link rel="top" title="mujoco-py ******** documentation" href="index.html"/> 

  
  <script src="_static/js/modernizr.min.js"></script>

</head>

<body class="wy-body-for-nav" role="document">

   
  <div class="wy-grid-for-nav">

    
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search">
          

          
            <a href="index.html" class="icon icon-home"> mujoco-py
          

          
          </a>

          
            
            
              <div class="version">
                ********
              </div>
            
          

          
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>

          
        </div>

        <div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
          
            
            
              
            
            
              <ul>
<li class="toctree-l1"><a class="reference internal" href="reference.html">API reference</a></li>
<li class="toctree-l1"><a class="reference internal" href="internals.html">Internals</a></li>
</ul>

            
          
        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap">

      
      <nav class="wy-nav-top" role="navigation" aria-label="top navigation">
        
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="index.html">mujoco-py</a>
        
      </nav>


      
      <div class="wy-nav-content">
        <div class="rst-content">
          















<div role="navigation" aria-label="breadcrumbs navigation">

  <ul class="wy-breadcrumbs">
    
      <li><a href="index.html">Docs</a> &raquo;</li>
        
      <li>Index</li>
    
    
      <li class="wy-breadcrumbs-aside">
        
            
        
      </li>
    
  </ul>

  
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
            

<h1 id="index">Index</h1>

<div class="genindex-jumpbox">
 <a href="#A"><strong>A</strong></a>
 | <a href="#B"><strong>B</strong></a>
 | <a href="#C"><strong>C</strong></a>
 | <a href="#E"><strong>E</strong></a>
 | <a href="#F"><strong>F</strong></a>
 | <a href="#G"><strong>G</strong></a>
 | <a href="#I"><strong>I</strong></a>
 | <a href="#L"><strong>L</strong></a>
 | <a href="#M"><strong>M</strong></a>
 | <a href="#N"><strong>N</strong></a>
 | <a href="#P"><strong>P</strong></a>
 | <a href="#Q"><strong>Q</strong></a>
 | <a href="#R"><strong>R</strong></a>
 | <a href="#S"><strong>S</strong></a>
 | <a href="#T"><strong>T</strong></a>
 | <a href="#U"><strong>U</strong></a>
 | <a href="#W"><strong>W</strong></a>
 | <a href="#X"><strong>X</strong></a>
 
</div>
<h2 id="A">A</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#act">act</a>
</li>
      <li><a href="reference.html#act_dot">act_dot</a>
</li>
      <li><a href="reference.html#active_contacts_efc_pos">active_contacts_efc_pos</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#actuator_force">actuator_force</a>
</li>
      <li><a href="reference.html#actuator_length">actuator_length</a>
</li>
      <li><a href="reference.html#actuator_moment">actuator_moment</a>
</li>
      <li><a href="reference.html#actuator_velocity">actuator_velocity</a>
</li>
  </ul></td>
</tr></table>

<h2 id="B">B</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#body_jacp">body_jacp</a>
</li>
      <li><a href="reference.html#body_jacr">body_jacr</a>
</li>
      <li><a href="reference.html#body_xmat">body_xmat</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#body_xpos">body_xpos</a>
</li>
      <li><a href="reference.html#body_xquat">body_xquat</a>
</li>
      <li><a href="reference.html#body_xvelp">body_xvelp</a>
</li>
      <li><a href="reference.html#body_xvelr">body_xvelr</a>
</li>
  </ul></td>
</tr></table>

<h2 id="C">C</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#cacc">cacc</a>
</li>
      <li><a href="reference.html#cam_xmat">cam_xmat</a>
</li>
      <li><a href="reference.html#cam_xpos">cam_xpos</a>
</li>
      <li><a href="reference.html#cdof">cdof</a>
</li>
      <li><a href="reference.html#cdof_dot">cdof_dot</a>
</li>
      <li><a href="reference.html#cfrc_ext">cfrc_ext</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#cfrc_int">cfrc_int</a>
</li>
      <li><a href="reference.html#cinert">cinert</a>
</li>
      <li><a href="reference.html#contact">contact</a>
</li>
      <li><a href="reference.html#crb">crb</a>
</li>
      <li><a href="reference.html#mujoco_py.MjSimPool.create_from_sim">create_from_sim() (mujoco_py.MjSimPool static method)</a>
</li>
      <li><a href="reference.html#ctrl">ctrl</a>
</li>
      <li><a href="reference.html#cvel">cvel</a>
</li>
  </ul></td>
</tr></table>

<h2 id="E">E</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#efc_AR">efc_AR</a>
</li>
      <li><a href="reference.html#efc_AR_colind">efc_AR_colind</a>
</li>
      <li><a href="reference.html#efc_AR_rowadr">efc_AR_rowadr</a>
</li>
      <li><a href="reference.html#efc_AR_rownnz">efc_AR_rownnz</a>
</li>
      <li><a href="reference.html#efc_aref">efc_aref</a>
</li>
      <li><a href="reference.html#efc_b">efc_b</a>
</li>
      <li><a href="reference.html#efc_D">efc_D</a>
</li>
      <li><a href="reference.html#efc_diagApprox">efc_diagApprox</a>
</li>
      <li><a href="reference.html#efc_force">efc_force</a>
</li>
      <li><a href="reference.html#efc_frictionloss">efc_frictionloss</a>
</li>
      <li><a href="reference.html#efc_id">efc_id</a>
</li>
      <li><a href="reference.html#efc_J">efc_J</a>
</li>
      <li><a href="reference.html#efc_J_colind">efc_J_colind</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#efc_J_rowadr">efc_J_rowadr</a>
</li>
      <li><a href="reference.html#efc_J_rownnz">efc_J_rownnz</a>
</li>
      <li><a href="reference.html#efc_JT">efc_JT</a>
</li>
      <li><a href="reference.html#efc_JT_colind">efc_JT_colind</a>
</li>
      <li><a href="reference.html#efc_JT_rowadr">efc_JT_rowadr</a>
</li>
      <li><a href="reference.html#efc_JT_rownnz">efc_JT_rownnz</a>
</li>
      <li><a href="reference.html#efc_margin">efc_margin</a>
</li>
      <li><a href="reference.html#efc_R">efc_R</a>
</li>
      <li><a href="reference.html#efc_solimp">efc_solimp</a>
</li>
      <li><a href="reference.html#efc_solref">efc_solref</a>
</li>
      <li><a href="reference.html#efc_state">efc_state</a>
</li>
      <li><a href="reference.html#efc_type">efc_type</a>
</li>
      <li><a href="reference.html#efc_vel">efc_vel</a>
</li>
      <li><a href="reference.html#energy">energy</a>
</li>
  </ul></td>
</tr></table>

<h2 id="F">F</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#mujoco_py.MjSimPool.forward">forward() (mujoco_py.MjSimPool method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="G">G</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#geom_jacp">geom_jacp</a>
</li>
      <li><a href="reference.html#geom_jacr">geom_jacr</a>
</li>
      <li><a href="reference.html#geom_xmat">geom_xmat</a>
</li>
      <li><a href="reference.html#geom_xpos">geom_xpos</a>
</li>
      <li><a href="reference.html#geom_xvelp">geom_xvelp</a>
</li>
      <li><a href="reference.html#geom_xvelr">geom_xvelr</a>
</li>
      <li><a href="reference.html#get_body_jacp">get_body_jacp()</a>
</li>
      <li><a href="reference.html#get_body_jacr">get_body_jacr()</a>
</li>
      <li><a href="reference.html#get_body_ximat">get_body_ximat()</a>
</li>
      <li><a href="reference.html#get_body_xipos">get_body_xipos()</a>
</li>
      <li><a href="reference.html#get_body_xmat">get_body_xmat()</a>
</li>
      <li><a href="reference.html#get_body_xpos">get_body_xpos()</a>
</li>
      <li><a href="reference.html#get_body_xquat">get_body_xquat()</a>
</li>
      <li><a href="reference.html#get_body_xvelp">get_body_xvelp()</a>
</li>
      <li><a href="reference.html#get_body_xvelr">get_body_xvelr()</a>
</li>
      <li><a href="reference.html#get_cam_xmat">get_cam_xmat()</a>
</li>
      <li><a href="reference.html#get_cam_xpos">get_cam_xpos()</a>
</li>
      <li><a href="reference.html#get_camera_xmat">get_camera_xmat()</a>
</li>
      <li><a href="reference.html#get_camera_xpos">get_camera_xpos()</a>
</li>
      <li><a href="reference.html#get_geom_jacp">get_geom_jacp()</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#get_geom_jacr">get_geom_jacr()</a>
</li>
      <li><a href="reference.html#get_geom_xmat">get_geom_xmat()</a>
</li>
      <li><a href="reference.html#get_geom_xpos">get_geom_xpos()</a>
</li>
      <li><a href="reference.html#get_geom_xvelp">get_geom_xvelp()</a>
</li>
      <li><a href="reference.html#get_geom_xvelr">get_geom_xvelr()</a>
</li>
      <li><a href="reference.html#get_joint_qpos">get_joint_qpos()</a>
</li>
      <li><a href="reference.html#get_joint_qvel">get_joint_qvel()</a>
</li>
      <li><a href="reference.html#get_joint_xanchor">get_joint_xanchor()</a>
</li>
      <li><a href="reference.html#get_joint_xaxis">get_joint_xaxis()</a>
</li>
      <li><a href="reference.html#get_light_xdir">get_light_xdir()</a>
</li>
      <li><a href="reference.html#get_light_xpos">get_light_xpos()</a>
</li>
      <li><a href="reference.html#get_mocap_pos">get_mocap_pos()</a>
</li>
      <li><a href="reference.html#get_mocap_quat">get_mocap_quat()</a>
</li>
      <li><a href="reference.html#get_site_jacp">get_site_jacp()</a>
</li>
      <li><a href="reference.html#get_site_jacr">get_site_jacr()</a>
</li>
      <li><a href="reference.html#get_site_xmat">get_site_xmat()</a>
</li>
      <li><a href="reference.html#get_site_xpos">get_site_xpos()</a>
</li>
      <li><a href="reference.html#get_site_xvelp">get_site_xvelp()</a>
</li>
      <li><a href="reference.html#get_site_xvelr">get_site_xvelr()</a>
</li>
      <li><a href="reference.html#mujoco_py.MjSim.get_state">get_state() (mujoco_py.MjSim method)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="I">I</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#mujoco_py.ignore_mujoco_warnings">ignore_mujoco_warnings() (in module mujoco_py)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="L">L</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#light_xdir">light_xdir</a>
</li>
      <li><a href="reference.html#light_xpos">light_xpos</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#mujoco_py.load_model_from_mjb">load_model_from_mjb() (in module mujoco_py)</a>
</li>
      <li><a href="reference.html#mujoco_py.load_model_from_path">load_model_from_path() (in module mujoco_py)</a>
</li>
      <li><a href="reference.html#mujoco_py.load_model_from_xml">load_model_from_xml() (in module mujoco_py)</a>
</li>
  </ul></td>
</tr></table>

<h2 id="M">M</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#maxuse_con">maxuse_con</a>
</li>
      <li><a href="reference.html#maxuse_efc">maxuse_efc</a>
</li>
      <li><a href="reference.html#maxuse_stack">maxuse_stack</a>
</li>
      <li><a href="reference.html#mujoco_py.MjSim">MjSim (class in mujoco_py)</a>
</li>
      <li><a href="reference.html#mujoco_py.MjSimPool">MjSimPool (class in mujoco_py)</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#mujoco_py.MjSimState">MjSimState (class in mujoco_py)</a>
</li>
      <li><a href="reference.html#mujoco_py.MjViewer">MjViewer (class in mujoco_py)</a>
</li>
      <li><a href="reference.html#mujoco_py.MjViewerBasic">MjViewerBasic (class in mujoco_py)</a>
</li>
      <li><a href="reference.html#mocap_pos">mocap_pos</a>
</li>
      <li><a href="reference.html#mocap_quat">mocap_quat</a>
</li>
  </ul></td>
</tr></table>

<h2 id="N">N</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#nbuffer">nbuffer</a>
</li>
      <li><a href="reference.html#ncon">ncon</a>
</li>
      <li><a href="reference.html#ne">ne</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#nefc">nefc</a>
</li>
      <li><a href="reference.html#nf">nf</a>
</li>
      <li><a href="reference.html#nstack">nstack</a>
</li>
  </ul></td>
</tr></table>

<h2 id="P">P</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#pstack">pstack</a>
</li>
  </ul></td>
</tr></table>

<h2 id="Q">Q</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#qacc">qacc</a>
</li>
      <li><a href="reference.html#qacc_unc">qacc_unc</a>
</li>
      <li><a href="reference.html#qacc_warmstart">qacc_warmstart</a>
</li>
      <li><a href="reference.html#qfrc_actuator">qfrc_actuator</a>
</li>
      <li><a href="reference.html#qfrc_applied">qfrc_applied</a>
</li>
      <li><a href="reference.html#qfrc_bias">qfrc_bias</a>
</li>
      <li><a href="reference.html#qfrc_constraint">qfrc_constraint</a>
</li>
      <li><a href="reference.html#qfrc_inverse">qfrc_inverse</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#qfrc_passive">qfrc_passive</a>
</li>
      <li><a href="reference.html#qfrc_unc">qfrc_unc</a>
</li>
      <li><a href="reference.html#qLD">qLD</a>
</li>
      <li><a href="reference.html#qLDiagInv">qLDiagInv</a>
</li>
      <li><a href="reference.html#qLDiagSqrtInv">qLDiagSqrtInv</a>
</li>
      <li><a href="reference.html#qM">qM</a>
</li>
      <li><a href="reference.html#qpos">qpos</a>
</li>
      <li><a href="reference.html#qvel">qvel</a>
</li>
  </ul></td>
</tr></table>

<h2 id="R">R</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#mujoco_py.MjSim.render">render() (mujoco_py.MjSim method)</a>

      <ul>
        <li><a href="reference.html#mujoco_py.MjViewer.render">(mujoco_py.MjViewer method)</a>
</li>
        <li><a href="reference.html#mujoco_py.MjViewerBasic.render">(mujoco_py.MjViewerBasic method)</a>
</li>
      </ul></li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#mujoco_py.MjSim.reset">reset() (mujoco_py.MjSim method)</a>

      <ul>
        <li><a href="reference.html#mujoco_py.MjSimPool.reset">(mujoco_py.MjSimPool method)</a>
</li>
      </ul></li>
  </ul></td>
</tr></table>

<h2 id="S">S</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#mujoco_py.MjSim.save">save() (mujoco_py.MjSim method)</a>
</li>
      <li><a href="reference.html#sensordata">sensordata</a>
</li>
      <li><a href="reference.html#set_joint_qpos">set_joint_qpos</a>
</li>
      <li><a href="reference.html#set_joint_qvel">set_joint_qvel</a>
</li>
      <li><a href="reference.html#set_mocap_pos">set_mocap_pos</a>
</li>
      <li><a href="reference.html#set_mocap_quat">set_mocap_quat</a>
</li>
      <li><a href="reference.html#mujoco_py.MjSim.set_state">set_state() (mujoco_py.MjSim method)</a>
</li>
      <li><a href="reference.html#mujoco_py.MjSim.set_state_from_flattened">set_state_from_flattened() (mujoco_py.MjSim method)</a>
</li>
      <li><a href="reference.html#site_jacp">site_jacp</a>
</li>
      <li><a href="reference.html#site_jacr">site_jacr</a>
</li>
      <li><a href="reference.html#site_xmat">site_xmat</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#site_xpos">site_xpos</a>
</li>
      <li><a href="reference.html#site_xvelp">site_xvelp</a>
</li>
      <li><a href="reference.html#site_xvelr">site_xvelr</a>
</li>
      <li><a href="reference.html#solver">solver</a>
</li>
      <li><a href="reference.html#solver_fwdinv">solver_fwdinv</a>
</li>
      <li><a href="reference.html#solver_iter">solver_iter</a>
</li>
      <li><a href="reference.html#solver_nnz">solver_nnz</a>
</li>
      <li><a href="reference.html#mujoco_py.MjSim.step">step() (mujoco_py.MjSim method)</a>

      <ul>
        <li><a href="reference.html#mujoco_py.MjSimPool.step">(mujoco_py.MjSimPool method)</a>
</li>
      </ul></li>
      <li><a href="reference.html#subtree_angmom">subtree_angmom</a>
</li>
      <li><a href="reference.html#subtree_com">subtree_com</a>
</li>
      <li><a href="reference.html#subtree_linvel">subtree_linvel</a>
</li>
  </ul></td>
</tr></table>

<h2 id="T">T</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#ten_length">ten_length</a>
</li>
      <li><a href="reference.html#ten_moment">ten_moment</a>
</li>
      <li><a href="reference.html#ten_velocity">ten_velocity</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#ten_wrapadr">ten_wrapadr</a>
</li>
      <li><a href="reference.html#ten_wrapnum">ten_wrapnum</a>
</li>
      <li><a href="reference.html#time">time</a>
</li>
      <li><a href="reference.html#timer">timer</a>
</li>
  </ul></td>
</tr></table>

<h2 id="U">U</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#userdata">userdata</a>
</li>
  </ul></td>
</tr></table>

<h2 id="W">W</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#warning">warning</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#wrap_obj">wrap_obj</a>
</li>
      <li><a href="reference.html#wrap_xpos">wrap_xpos</a>
</li>
  </ul></td>
</tr></table>

<h2 id="X">X</h2>
<table style="width: 100%" class="indextable genindextable"><tr>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#xanchor">xanchor</a>
</li>
      <li><a href="reference.html#xaxis">xaxis</a>
</li>
  </ul></td>
  <td style="width: 33%; vertical-align: top;"><ul>
      <li><a href="reference.html#xfrc_applied">xfrc_applied</a>
</li>
      <li><a href="reference.html#ximat">ximat</a>
</li>
      <li><a href="reference.html#xipos">xipos</a>
</li>
  </ul></td>
</tr></table>



           </div>
           <div class="articleComments">
            
           </div>
          </div>
          <footer>
  

  <hr/>

  <div role="contentinfo">
    <p>
        &copy; Copyright 2017, OpenAI.

    </p>
  </div>
  Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a href="https://github.com/snide/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the Docs</a>. 

</footer>

        </div>
      </div>

    </section>

  </div>
  


  

    <script type="text/javascript">
        var DOCUMENTATION_OPTIONS = {
            URL_ROOT:'./',
            VERSION:'********',
            COLLAPSE_INDEX:false,
            FILE_SUFFIX:'.html',
            HAS_SOURCE:  true,
            SOURCELINK_SUFFIX: '.txt'
        };
    </script>
      <script type="text/javascript" src="_static/jquery.js"></script>
      <script type="text/javascript" src="_static/underscore.js"></script>
      <script type="text/javascript" src="_static/doctools.js"></script>

  

  
  
    <script type="text/javascript" src="_static/js/theme.js"></script>
  

  
  
  <script type="text/javascript">
      jQuery(function () {
          SphinxRtdTheme.StickyNav.enable();
      });
  </script>
   

</body>
</html>