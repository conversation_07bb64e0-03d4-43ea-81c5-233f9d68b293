---
name: Bug report
about: Create a report to help us improve

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior.

**Expected behavior**
A clear and concise description of what you expected to happen.

**Error Messages**
Including more/longer error messages gives us more information to work with.

**Desktop (please complete the following information):**
 - OS: [e.g. macOS 10.13.6]
 - Python Version [e.g. 3.6.6]
 - Mujoco Version [e.g. 1.50]
 - mujoco-py version [e.g. *********]

**Environment**
 - output of: `echo $LD_LIBRARY_PATH`
 - output of: `echo $HOME`
 - output of: `echo $USER`

**Additional context**
Add any other context about the problem here.
