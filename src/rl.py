import robosuite as suite
import os
import yaml

from robosuite.wrappers.gym_wrapper import GymWrapper
from robosuite import load_controller_config
from robosuite.environments.base import register_env

from stable_baselines3 import PPO
from stable_baselines3.common.save_util import save_to_zip_file, load_from_zip_file
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common.vec_env import DummyVecEnv, VecNormalize, SubprocVecEnv
from stable_baselines3.common.env_util import make_vec_env
from stable_baselines3.common.utils import set_random_seed
# from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common.callbacks import <PERSON><PERSON><PERSON><PERSON>back, CheckpointCallback

from typing import Callable

from my_models.grippers import UltrasoundProbeGripper
from my_environments import Ultrasound
from utils.common import register_gripper

import gym
import multiprocessing as mp


def make_robosuite_env(env_id, options, rank, seed=0):
    """
    Utility function for multiprocessed env.

    :param env_id: (str) the environment ID
    :param options: (dict) additional arguments to pass to the specific environment class initializer
    :param seed: (int) the inital seed for RNG
    :param rank: (int) index of the subprocess
    """
    def _init():
        # Only enable renderer for the first environment to avoid multiple windows
        if rank == 0 and options.get('has_renderer', False):
            local_options = options.copy()
            print(f"Enabling renderer for environment {rank}")
        else:
            local_options = options.copy()
            local_options['has_renderer'] = False

        register_gripper(UltrasoundProbeGripper)
        env = GymWrapper(suite.make(env_id, **local_options))
        env = Monitor(env)
        env.seed(seed + rank)
        return env
    set_random_seed(seed)
    return _init


def make_gym_env(env_id, rank, seed=0):
    """
    Utility function for multiprocessed env.

    :param env_id: (str) the environment ID
    :param seed: (int) the inital seed for RNG
    :param rank: (int) index of the subprocess
    """
    def _init():
        env = gym.make(env_id, reward_type="dense")
        env = gym.wrappers.FlattenObservation(env)
        env = Monitor(env)
        env.seed(seed + rank)
        return env
    set_random_seed(seed)
    return _init


def linear_schedule(initial_value: float) -> Callable[[float], float]:
    """
    Linear learning rate schedule.

    :param initial_value: Initial learning rate.
    :return: schedule that computes
      current learning rate depending on remaining progress
    """
    def func(progress_remaining: float) -> float:
        """
        Progress will decrease from 1 (beginning) to 0.

        :param progress_remaining:
        :return: current learning rate
        """
        return progress_remaining * initial_value

    return func





if __name__ == '__main__':

    mp.set_start_method('spawn', force=True)  # add this!

    register_env(Ultrasound)

    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument("--config", type=str, default="rl_config.yaml", help="Path to the configuration file")
    args = parser.parse_args()

    with open(args.config, 'r') as stream:
        config = yaml.safe_load(stream)

    # Environment specifications
    env_options = config["robosuite"]
    env_id = env_options.pop("env_id")

    # Settings for stable-baselines RL algorithm
    sb_config = config["sb_config"]
    training_timesteps = sb_config["total_timesteps"]
    check_pt_interval = sb_config["check_pt_interval"]
    num_cpu = sb_config["num_cpu"]

    # Settings for stable-baselines policy
    policy_kwargs = config["sb_policy"]
    policy_type = policy_kwargs.pop("type")

    # Settings used for file handling and logging (save/load destination etc)
    file_handling = config["file_handling"]

    tb_log_folder = file_handling["tb_log_folder"]
    tb_log_name = file_handling["tb_log_name"]

    save_model_folder = file_handling["save_model_folder"]
    save_model_filename = file_handling["save_model_filename"]
    load_model_folder = file_handling["load_model_folder"]
    load_model_filename = file_handling["load_model_filename"]
    load_vecnormalize_filename = file_handling.get("load_vecnormalize_filename", 'vec_normalize_' + load_model_filename)

    continue_training_model_folder = file_handling["continue_training_model_folder"]
    continue_training_model_filename = file_handling["continue_training_model_filename"]

    # Join paths
    save_model_path = os.path.join(save_model_folder, save_model_filename)
    save_vecnormalize_path = os.path.join(save_model_folder, 'vec_normalize_' + save_model_filename + '.pkl')
    load_model_path = os.path.join(load_model_folder, load_model_filename)
    load_vecnormalize_path = os.path.join(load_model_folder, load_vecnormalize_filename + '.pkl')

    # Settings for pipeline
    training = config["training"]
    seed = config["seed"]
    # RL pipeline
    if training:
        env = SubprocVecEnv([make_robosuite_env(env_id, env_options, i, seed) for i in range(num_cpu)])

        # Create callback
        checkpoint_callback = CheckpointCallback(save_freq=check_pt_interval, save_path='./checkpoints/',
                                name_prefix=save_model_filename, verbose=2)

        # Train new model
        if continue_training_model_filename is None:

            # Normalize environment
            env = VecNormalize(env)

            # Create model
            model = PPO(policy_type, env, policy_kwargs=policy_kwargs, tensorboard_log=tb_log_folder, verbose=1)

            print("Created a new model")

        # Continual training
        else:

            # Join file paths
            continue_training_model_path = os.path.join(continue_training_model_folder, continue_training_model_filename)
            continue_training_vecnormalize_path = os.path.join(continue_training_model_folder, 'vec_normalize_' + continue_training_model_filename + '.pkl')

            print(f"Continual training on model located at {continue_training_model_path}")

            # Load normalized env
            env = VecNormalize.load(continue_training_vecnormalize_path, env)

            # Load model
            model = PPO.load(continue_training_model_path, env=env)

        # Training
        model.learn(total_timesteps=training_timesteps, tb_log_name=tb_log_name, callback=checkpoint_callback, reset_num_timesteps=True)

        # Save trained model
        model.save(save_model_path)
        env.save(save_vecnormalize_path)

    else:
        # Create evaluation environment
        # Use the renderer setting from config file
        print(f"Using renderer: {env_options.get('has_renderer', False)}")
        register_gripper(UltrasoundProbeGripper)
        env_gym = GymWrapper(suite.make(env_id, **env_options))
        env = DummyVecEnv([lambda : env_gym])

        # Load normalized env
        env = VecNormalize.load(load_vecnormalize_path, env)

        # Turn of updates and reward normalization
        env.training = False
        env.norm_reward = False

        # Load model
        model = PPO.load(load_model_path, env)

        # Simulate environment
        obs = env.reset()
        eprew = 0
        step_count = 0
        while True:
            step_count += 1
            action, _states = model.predict(obs)
            print(f"action: {action}")
            obs, reward, done, info = env.step(action)
            #print(action)
            print(f'reward: {reward}')
            eprew += reward
            # Try to render or save images
            try:
                if env_options.get('has_renderer', False):
                    env_gym.render()
                elif env_options.get('has_offscreen_renderer', False) and step_count % 10 == 0:  # Save every 10th frame
                    img = env_gym.sim.render(camera_name="agentview", width=500, height=500)
                    import imageio
                    import os
                    # Create directory if it doesn't exist
                    os.makedirs("simulation_images", exist_ok=True)
                    # Save image
                    imageio.imwrite(f"simulation_images/frame_{step_count}.png", img)
                    print(f"Saved frame_{step_count}.png")
            except Exception as e:
                print(f"Rendering error: {e}")
            if done:
                print(f'eprew: {eprew}')
                obs = env.reset()
                eprew = 0
                step_count = 0

        env.close()


