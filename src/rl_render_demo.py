#!/usr/bin/env python3

import robosuite as suite
import os
import yaml
import numpy as np

from robosuite.wrappers.gym_wrapper import GymWrapper
from robosuite.environments.base import register_env

from stable_baselines3 import PPO
from stable_baselines3.common.vec_env import DummyVecEnv, VecNormalize
from stable_baselines3.common.utils import set_random_seed

from my_models.grippers import UltrasoundProbeGripper
from my_environments import Ultrasound
from utils.common import register_gripper

def create_working_rl_demo():
    """Create a working RL demo with rendering that bypasses model loading issues"""

    # Register custom components
    register_env(Ultrasound)
    register_gripper(UltrasoundProbeGripper)

    # Load config
    with open('rl_config.yaml', 'r') as stream:
        config = yaml.safe_load(stream)

    env_options = config["robosuite"]
    env_id = env_options.pop("env_id")

    # Enable offscreen rendering for image saving
    env_options["has_renderer"] = False
    env_options["has_offscreen_renderer"] = True

    print("Creating environment with offscreen rendering...")

    # Create environment
    env_gym = GymWrapper(suite.make(env_id, **env_options))
    env = DummyVecEnv([lambda: env_gym])

    print(f"Environment created successfully!")
    print(f"Observation space: {env.observation_space}")
    print(f"Action space: {env.action_space}")

    # Instead of loading a pre-trained model, let's create a simple random policy demo
    print("\n=== Running Random Policy Demo with Rendering ===")

    obs = env.reset()
    step_count = 0
    episode_count = 0
    max_episodes = 3
    max_steps_per_episode = 100

    # Create directory for saving images
    os.makedirs("demo_images", exist_ok=True)

    while episode_count < max_episodes:
        step_count += 1

        # Random action (you can replace this with a trained model later)
        action = np.array([env.action_space.sample()])

        # Step environment
        obs, reward, done, info = env.step(action)

        print(f"Episode {episode_count + 1}, Step {step_count}: reward={reward[0]:.3f}")

        # Save rendered image every 10 steps
        if step_count % 10 == 0:
            try:
                # Render the environment and save image
                img = env_gym.sim.render(camera_name="agentview", width=500, height=500)

                import imageio
                filename = f"demo_images/episode_{episode_count + 1}_step_{step_count}.png"
                imageio.imwrite(filename, img)
                print(f"  💾 Saved image: {filename}")

            except Exception as e:
                print(f"  ❌ Rendering error: {e}")

        # Check if episode is done
        if done[0] or step_count >= max_steps_per_episode:
            episode_count += 1
            step_count = 0
            print(f"✅ Episode {episode_count} completed!")

            if episode_count < max_episodes:
                obs = env.reset()
                print(f"\n🔄 Starting episode {episode_count + 1}...")

    print(f"\n🎉 Demo completed! Check the 'demo_images' folder for rendered frames.")
    env.close()

def create_simple_trained_model_demo():
    """Create a simple trained model for demonstration"""

    # Register custom components
    register_env(Ultrasound)
    register_gripper(UltrasoundProbeGripper)

    # Load config
    with open('rl_config.yaml', 'r') as stream:
        config = yaml.safe_load(stream)

    env_options = config["robosuite"]
    env_id = env_options.pop("env_id")

    # Disable rendering for training
    env_options["has_renderer"] = False
    env_options["has_offscreen_renderer"] = False

    print("Creating environment for training a simple model...")

    # Create environment
    env_gym = GymWrapper(suite.make(env_id, **env_options))
    env = DummyVecEnv([lambda: env_gym])

    # Normalize environment
    env = VecNormalize(env)

    # Create and train a simple model
    print("Training a simple PPO model for 1000 steps...")
    model = PPO("MlpPolicy", env, verbose=1)
    model.learn(total_timesteps=1000)

    # Save the model
    model.save("demo_model")
    env.save("demo_vec_normalize.pkl")

    print("✅ Simple model trained and saved!")

    # Now test the trained model with rendering
    print("\n=== Testing Trained Model with Rendering ===")

    # Create new environment with rendering
    env_options["has_renderer"] = False
    env_options["has_offscreen_renderer"] = True

    env_gym_render = GymWrapper(suite.make(env_id, **env_options))
    env_render = DummyVecEnv([lambda: env_gym_render])

    # Load the normalization
    env_render = VecNormalize.load("demo_vec_normalize.pkl", env_render)
    env_render.training = False
    env_render.norm_reward = False

    # Load the model
    model = PPO.load("demo_model", env=env_render)

    # Run the trained model
    obs = env_render.reset()
    os.makedirs("trained_demo_images", exist_ok=True)

    for step in range(50):
        action, _states = model.predict(obs, deterministic=True)
        obs, reward, done, info = env_render.step(action)

        print(f"Step {step + 1}: reward={reward[0]:.3f}")

        # Save image every 5 steps
        if (step + 1) % 5 == 0:
            try:
                img = env_gym_render.sim.render(camera_name="agentview", width=500, height=500)
                import imageio
                filename = f"trained_demo_images/trained_step_{step + 1}.png"
                imageio.imwrite(filename, img)
                print(f"  💾 Saved trained model image: {filename}")
            except Exception as e:
                print(f"  ❌ Rendering error: {e}")

        if done[0]:
            obs = env_render.reset()

    print("🎉 Trained model demo completed!")
    env_render.close()

if __name__ == "__main__":
    print("🤖 Robotic Ultrasound RL Demo")
    print("=" * 50)

    choice = input("Choose demo type:\n1. Random policy with rendering\n2. Train simple model and test\nEnter choice (1 or 2): ")

    if choice == "1":
        create_working_rl_demo()
    elif choice == "2":
        create_simple_trained_model_demo()
    else:
        print("Invalid choice. Running random policy demo...")
        create_working_rl_demo()
