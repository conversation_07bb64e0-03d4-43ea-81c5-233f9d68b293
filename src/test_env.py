#!/usr/bin/env python3

import robosuite as suite
from robosuite.wrappers.gym_wrapper import Gym<PERSON>rapper
from robosuite.environments.base import register_env

from my_models.grippers import Ultrasound<PERSON>robeGripper
from my_environments import Ultrasound
from utils.common import register_gripper

def test_environment():
    """Test if the environment can be created and run"""
    
    # Register custom components
    register_env(Ultrasound)
    register_gripper(UltrasoundProbeGripper)
    
    # Environment options from config
    env_options = {
        "robots": "Panda",
        "use_camera_obs": False,
        "use_object_obs": False,
        "has_renderer": False,
        "has_offscreen_renderer": False,
        "render_camera": None,
        "control_freq": 500,
        "horizon": 1000,
        "camera_names": "agentview",
        "camera_heights": 48,
        "camera_widths": 48,
        "camera_depths": False,
        "reward_shaping": True,
        "controller_configs": {
            "type": "OSC_POSE",
            "input_max": 1,
            "input_min": -1,
            "output_max": [0.05, 0.05, 0.05, 0.5, 0.5, 0.5],
            "output_min": [-0.05, -0.05, -0.05, -0.5, -0.5, -0.5],
            "kp": 300,
            "damping_ratio": 1,
            "impedance_mode": "tracking",
            "kp_limits": [0, 500],
            "kp_input_max": 1,
            "kp_input_min": 0,
            "damping_ratio_limits": [0, 2],
            "position_limits": None,
            "orientation_limits": None,
            "uncouple_pos_ori": True,
            "control_delta": True,
            "interpolation": None,
            "ramp_ratio": 0.2
        },
        "early_termination": True,
        "save_data": False,
        "deterministic_trajectory": False,
        "torso_solref_randomization": True,
        "initial_probe_pos_randomization": True,
        "use_box_torso": True
    }
    
    print("Creating robosuite environment...")
    try:
        env = suite.make("Ultrasound", **env_options)
        print("✓ Robosuite environment created successfully")
        print(f"  Action space: {env.action_spec}")
        print(f"  Observation keys: {list(env._get_observations().keys())}")
    except Exception as e:
        print(f"✗ Failed to create robosuite environment: {e}")
        return False
    
    print("\nCreating Gym wrapper...")
    try:
        gym_env = GymWrapper(env)
        print("✓ Gym wrapper created successfully")
        print(f"  Action space: {gym_env.action_space}")
        print(f"  Observation space: {gym_env.observation_space}")
    except Exception as e:
        print(f"✗ Failed to create Gym wrapper: {e}")
        return False
    
    print("\nTesting environment reset...")
    try:
        obs = gym_env.reset()
        print("✓ Environment reset successful")
        print(f"  Observation shape: {obs.shape}")
    except Exception as e:
        print(f"✗ Failed to reset environment: {e}")
        return False
    
    print("\nTesting environment step...")
    try:
        action = gym_env.action_space.sample()
        obs, reward, done, info = gym_env.step(action)
        print("✓ Environment step successful")
        print(f"  Reward: {reward}")
        print(f"  Done: {done}")
    except Exception as e:
        print(f"✗ Failed to step environment: {e}")
        return False
    
    print("\n✓ All tests passed! Environment is working correctly.")
    return True

if __name__ == "__main__":
    test_environment()
