#!/usr/bin/env python3

import robosuite as suite
import os
import yaml
import pickle
import zipfile

from robosuite.wrappers.gym_wrapper import GymWrapper
from robosuite.environments.base import register_env

from stable_baselines3 import PPO
from stable_baselines3.common.vec_env import DummyVecEnv, VecNormalize

from my_models.grippers import UltrasoundProbeGripper
from my_environments import Ultrasound
from utils.common import register_gripper

def debug_observation_spaces():
    """Debug observation space mismatches"""

    # Register custom components
    register_env(Ultrasound)
    register_gripper(UltrasoundProbeGripper)

    # Load config
    with open('rl_config.yaml', 'r') as stream:
        config = yaml.safe_load(stream)

    env_options = config["robosuite"]
    env_id = env_options.pop("env_id")

    # Force headless mode
    env_options["has_renderer"] = False
    env_options["has_offscreen_renderer"] = False

    # Create current environment
    print("=== Current Environment ===")
    env_gym = GymWrapper(suite.make(env_id, **env_options))
    env = DummyVecEnv([lambda: env_gym])

    print(f"Observation space: {env.observation_space}")
    print(f"  Shape: {env.observation_space.shape}")
    print(f"  Dtype: {env.observation_space.dtype}")
    print(f"  Low: {env.observation_space.low}")
    print(f"  High: {env.observation_space.high}")

    # Test with one of the models
    model_name = "tracking"
    model_path = f"trained_rl_models/{model_name}.zip"
    vec_normalize_path = f"trained_rl_models/vec_normalize_{model_name}.pkl"

    print(f"\n=== Model: {model_name} ===")

    # Load and inspect the model file
    try:
        with zipfile.ZipFile(model_path, 'r') as archive:
            # List files in the archive
            print(f"Files in model archive: {archive.namelist()}")

            # Try different ways to load the data
            if 'data' in archive.namelist():
                data_content = archive.read('data')
                print(f"Data content type: {type(data_content)}")
                print(f"Data content preview: {data_content[:100]}")

                # Try to load as pickle
                try:
                    data = pickle.loads(data_content)
                    print(f"Successfully loaded as pickle")
                except:
                    print("Failed to load as pickle, trying as JSON...")
                    import json
                    try:
                        data = json.loads(data_content.decode('utf-8'))
                        print(f"Successfully loaded as JSON")
                    except:
                        print("Failed to load as JSON too")
                        data = None

                if data and 'observation_space' in data:
                    print(f"Model observation space: {data['observation_space']}")
                    print(f"Model action space: {data['action_space']}")
                else:
                    print("Could not extract observation space from model file")
            else:
                print("No 'data' file found in archive")

    except Exception as e:
        print(f"Error reading model file: {e}")

    # Load VecNormalize and check its observation space
    print(f"\n=== VecNormalize ===")
    with open(vec_normalize_path, 'rb') as f:
        vec_normalize_data = pickle.load(f)

    print(f"VecNormalize keys: {vec_normalize_data.keys()}")
    if 'observation_space' in vec_normalize_data:
        print(f"VecNormalize observation space: {vec_normalize_data['observation_space']}")

    # Try to load VecNormalize with current env
    try:
        env_normalized = VecNormalize.load(vec_normalize_path, env)
        print(f"Loaded VecNormalize observation space: {env_normalized.observation_space}")
        print(f"  Shape: {env_normalized.observation_space.shape}")
        print(f"  Dtype: {env_normalized.observation_space.dtype}")
        print(f"  Low: {env_normalized.observation_space.low}")
        print(f"  High: {env_normalized.observation_space.high}")

        # Compare spaces element by element
        current_space = env_normalized.observation_space
        model_space = data['observation_space']

        print(f"\n=== Detailed Comparison ===")
        print(f"Shapes equal: {current_space.shape == model_space.shape}")
        print(f"Dtypes equal: {current_space.dtype == model_space.dtype}")
        print(f"Lows equal: {(current_space.low == model_space.low).all()}")
        print(f"Highs equal: {(current_space.high == model_space.high).all()}")

        # Check if there are any NaN or inf differences
        import numpy as np
        print(f"Current low has inf: {np.isinf(current_space.low).any()}")
        print(f"Current high has inf: {np.isinf(current_space.high).any()}")
        print(f"Model low has inf: {np.isinf(model_space.low).any()}")
        print(f"Model high has inf: {np.isinf(model_space.high).any()}")

    except Exception as e:
        print(f"Failed to load VecNormalize: {e}")

if __name__ == "__main__":
    debug_observation_spaces()
