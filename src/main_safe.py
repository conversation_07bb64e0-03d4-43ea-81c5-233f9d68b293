#!/usr/bin/env python3

import robosuite as suite
import os
import sys

# Try different rendering backends to avoid GLEW errors
def setup_rendering():
    """Setup rendering backend to avoid GLEW errors"""

    # Try different backends in order of preference
    backends = ['osmesa', 'egl', 'glfw']

    for backend in backends:
        try:
            print(f"Trying rendering backend: {backend}")
            os.environ['MUJOCO_GL'] = backend
            if backend == 'egl':
                os.environ['PYOPENGL_PLATFORM'] = 'egl'
            elif backend == 'osmesa':
                os.environ['PYOPENGL_PLATFORM'] = 'osmesa'

            # Test if backend works by importing mujoco_py
            import mujoco_py
            print(f"✅ Successfully set up {backend} backend")
            return backend

        except Exception as e:
            print(f"❌ Backend {backend} failed: {e}")
            continue

    print("⚠️  No working rendering backend found. Proceeding without rendering...")
    return None

# Setup rendering before importing robosuite components
rendering_backend = setup_rendering()

from robosuite.environments.base import register_env
from my_environments import Ultrasound, HMFC
from my_models.grippers import Ultrasound<PERSON>robeGripper
from utils.common import register_gripper

# Register custom components
register_env(Ultrasound)
register_env(HMFC)
register_gripper(UltrasoundProbeGripper)

def run_safe_simulation():
    """Run simulation with error handling for graphics issues"""

    print("🤖 Starting Safe Ultrasound Simulation...")
    print(f"Using rendering backend: {rendering_backend}")

    env_id = "Ultrasound"

    # Environment options - completely headless
    env_options = {
        "robots": "Panda",
        "gripper_types": "UltrasoundProbeGripper",
        "controller_configs": {
            "type": "OSC_POSE",
            "input_max": 1,
            "input_min": -1,
            "output_max": [0.05, 0.05, 0.05, 0.5, 0.5, 0.5],
            "output_min": [-0.05, -0.05, -0.05, -0.5, -0.5, -0.5],
            "kp": 300,
            "damping_ratio": 1,
            "impedance_mode": "fixed",
            "kp_limits": [0, 500],
            "kp_input_max": 1,
            "kp_input_min": 0,
            "damping_ratio_limits": [0, 2],
            "position_limits": None,
            "orientation_limits": None,
            "uncouple_pos_ori": True,
            "control_delta": True,
            "interpolation": None,
            "ramp_ratio": 0.2
        },
        "control_freq": 500,
        "has_renderer": False,
        "has_offscreen_renderer": False,
        "render_camera": None,
        "use_camera_obs": False,
        "use_object_obs": False,
        "horizon": 50,  # Short horizon for testing
        "early_termination": False,
        "save_data": False,
        "torso_solref_randomization": False,
        "initial_probe_pos_randomization": False,
        "deterministic_trajectory": False,
        "use_box_torso": True
    }

    try:
        print("Creating environment...")
        env = suite.make(env_id, **env_options)
        print("✅ Environment created successfully!")

        print("Resetting environment...")
        obs = env.reset()
        print("✅ Environment reset complete!")
        print(f"Observation shape: {obs.shape if hasattr(obs, 'shape') else 'N/A'}")

        done = False
        total_reward = 0.0

        print(f"Starting simulation for {env.horizon} steps...")

        for step in range(env.horizon):
            # Simple action - no movement
            action = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0]

            # Step the environment
            obs, reward, done, info = env.step(action)
            total_reward += reward

            # Print progress
            if step % 10 == 0 or step < 5:
                print(f"Step {step:3d}: reward={reward:6.3f}, total={total_reward:6.3f}")

            if done:
                print(f"Episode terminated early at step {step}")
                break

        env.close()
        print(f"✅ Simulation completed successfully!")
        print(f"Final total reward: {total_reward:.3f}")

        return True

    except Exception as e:
        print(f"❌ Error during simulation: {e}")
        print(f"Error type: {type(e).__name__}")

        # Provide specific help for common errors
        if "GLEW" in str(e):
            print("\n💡 GLEW Error Solutions:")
            print("1. Install mesa-utils: sudo apt-get install mesa-utils")
            print("2. Try: export MUJOCO_GL=osmesa")
            print("3. Try: export DISPLAY=:0 (if using X11)")
            print("4. Install EGL: sudo apt-get install libegl1-mesa-dev")

        elif "OpenGL" in str(e):
            print("\n💡 OpenGL Error Solutions:")
            print("1. Update graphics drivers")
            print("2. Try software rendering: export LIBGL_ALWAYS_SOFTWARE=1")
            print("3. Install OpenGL libraries: sudo apt-get install libgl1-mesa-glx")

        return False

def test_environment_creation():
    """Test if we can create the environment without running simulation"""

    print("🧪 Testing environment creation...")

    try:
        # Minimal environment options
        env_options = {
            "robots": "Panda",
            "gripper_types": "UltrasoundProbeGripper",
            "has_renderer": False,
            "has_offscreen_renderer": False,
            "use_camera_obs": False,  # Disable camera observations
            "horizon": 10
        }

        env = suite.make("Ultrasound", **env_options)
        print("✅ Environment creation test passed!")
        env.close()
        return True

    except Exception as e:
        print(f"❌ Environment creation test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Robotic Ultrasound Simulation - Safe Mode")
    print("=" * 50)

    # Test environment creation first
    if test_environment_creation():
        # If creation works, try full simulation
        success = run_safe_simulation()

        if success:
            print("\n🎉 All tests passed! The simulation is working correctly.")
        else:
            print("\n😞 Simulation failed. Check the error messages above for solutions.")
    else:
        print("\n😞 Basic environment creation failed. Check your robosuite installation.")

    print("\n" + "=" * 50)
    print("Simulation complete!")
