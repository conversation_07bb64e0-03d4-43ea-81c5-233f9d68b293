#!/usr/bin/env python3

import robosuite as suite
import os
import yaml
import numpy as np

from robosuite.wrappers.gym_wrapper import GymWrapper
from robosuite.environments.base import register_env

from stable_baselines3 import PPO
from stable_baselines3.common.vec_env import DummyVecEnv, VecNormalize

from my_models.grippers import UltrasoundProbeGripper
from my_environments import Ultrasound
from utils.common import register_gripper

def test_model_loading():
    """Test loading different trained models and check observation spaces"""
    
    # Register custom components
    register_env(Ultrasound)
    register_gripper(UltrasoundProbeGripper)
    
    # Load config
    with open('rl_config.yaml', 'r') as stream:
        config = yaml.safe_load(stream)
    
    env_options = config["robosuite"]
    env_id = env_options.pop("env_id")
    
    # Create environment
    print("Creating environment...")
    env_gym = GymWrapper(suite.make(env_id, **env_options))
    env = DummyVecEnv([lambda: env_gym])
    
    print(f"Environment observation space: {env.observation_space}")
    print(f"Environment action space: {env.action_space}")
    
    # Test different models
    models = ["tracking", "variable_z", "wrench"]
    
    for model_name in models:
        print(f"\n--- Testing model: {model_name} ---")
        
        model_path = f"trained_rl_models/{model_name}.zip"
        vec_normalize_path = f"trained_rl_models/vec_normalize_{model_name}.pkl"
        
        if not os.path.exists(model_path):
            print(f"❌ Model file not found: {model_path}")
            continue
            
        if not os.path.exists(vec_normalize_path):
            print(f"❌ VecNormalize file not found: {vec_normalize_path}")
            continue
        
        try:
            # Load VecNormalize first
            print(f"Loading VecNormalize from: {vec_normalize_path}")
            env_normalized = VecNormalize.load(vec_normalize_path, env)
            env_normalized.training = False
            env_normalized.norm_reward = False
            
            print(f"VecNormalize observation space: {env_normalized.observation_space}")
            
            # Load model
            print(f"Loading model from: {model_path}")
            model = PPO.load(model_path, env=env_normalized)
            
            print(f"✅ Successfully loaded model: {model_name}")
            
            # Test prediction
            obs = env_normalized.reset()
            action, _states = model.predict(obs, deterministic=True)
            print(f"Test prediction successful. Action shape: {action.shape}")
            
            # Test a few steps
            for i in range(3):
                obs, reward, done, info = env_normalized.step(action)
                action, _states = model.predict(obs, deterministic=True)
                print(f"Step {i+1}: reward={reward[0]:.3f}, done={done[0]}")
                
                if done[0]:
                    obs = env_normalized.reset()
                    break
            
            print(f"✅ Model {model_name} works correctly!")
            return model_name, env_normalized, model  # Return the working model
            
        except Exception as e:
            print(f"❌ Failed to load model {model_name}: {e}")
            continue
    
    print("\n❌ No working models found!")
    return None, None, None

if __name__ == "__main__":
    working_model, env, model = test_model_loading()
    if working_model:
        print(f"\n🎉 Found working model: {working_model}")
        print("You can use this model for rendering!")
    else:
        print("\n😞 No working models found. You may need to retrain or check the model files.")
